# Elasticsearch数据定时清理脚本

## 功能说明

本脚本用于定时清理Elasticsearch中的历史数据，具体功能如下：

1. **device_ai索引**: 根据`timestamp`字段删除3个月前的数据（时间戳格式为秒）
2. **meter_cur_data_es索引**: 根据`dataTime`字段删除3个月前的数据（时间戳格式为毫秒）

## 文件说明

- `delete_es_data.sh`: 主要的数据清理脚本
- `crontab_config.txt`: crontab定时任务配置示例
- `README.md`: 使用说明文档

## 安装和配置

### 1. 脚本配置

编辑`delete_es_data.sh`文件，根据您的环境修改以下配置：

```bash
# Elasticsearch服务器配置
ES_HOST="localhost"        # 修改为您的ES服务器地址
ES_PORT=9200              # 修改为您的ES服务器端口

# 日志文件路径
LOG_FILE="/var/log/es_data_cleanup.log"  # 修改为合适的日志路径
```

### 2. 设置执行权限

```bash
chmod +x delete_es_data.sh
```

### 3. 手动测试

在设置定时任务前，建议先手动执行脚本进行测试：

```bash
./delete_es_data.sh
```

检查日志文件确认脚本正常运行：

```bash
tail -f /var/log/es_data_cleanup.log
```

### 4. 设置定时任务

编辑crontab：

```bash
crontab -e
```

添加定时任务（每天凌晨2点执行）：

```bash
0 2 * * * /path/to/delete_es_data.sh
```

## 脚本特性

### 日志记录
- 详细记录每次执行的时间、操作和结果
- 包含删除的记录数量统计
- 自动清理30天前的旧日志文件

### 安全性
- 使用精确的时间范围查询，避免误删数据
- 详细的日志记录便于审计和问题排查
- 支持查看索引当前状态

### 时间计算
- **device_ai**: 使用秒级时间戳，计算3个月前（90天）的时间点
- **meter_cur_data_es**: 使用毫秒级时间戳，计算3个月前（90天）的时间点

## 监控和维护

### 查看执行日志
```bash
tail -f /var/log/es_data_cleanup.log
```

### 检查crontab任务
```bash
crontab -l
```

### 手动检查索引状态
```bash
# 检查device_ai索引文档数量
curl -X GET "http://localhost:9200/device_ai/_count"

# 检查meter_cur_data_es索引文档数量
curl -X GET "http://localhost:9200/meter_cur_data_es/_count"
```

## 注意事项

1. **权限要求**: 确保运行脚本的用户有访问Elasticsearch和写入日志文件的权限
2. **网络连接**: 确保脚本运行环境能够访问Elasticsearch服务器
3. **存储空间**: 删除大量数据可能需要一些时间，建议在业务低峰期执行
4. **备份策略**: 建议在首次运行前确认有适当的数据备份
5. **测试环境**: 建议先在测试环境验证脚本功能

## 故障排查

### 常见问题

1. **连接失败**: 检查ES_HOST和ES_PORT配置是否正确
2. **权限错误**: 检查用户是否有足够权限
3. **日志文件无法写入**: 检查LOG_FILE路径和权限
4. **时间计算错误**: 检查系统时间是否正确

### 调试方法

可以在脚本中添加调试信息，或者手动执行curl命令进行测试：

```bash
# 测试连接
curl -X GET "http://localhost:9200/_cluster/health"

# 测试查询
curl -X GET "http://localhost:9200/device_ai/_search" -H 'Content-Type: application/json' -d '{
  "size": 0,
  "query": {
    "range": {
      "timestamp": {
        "lt": "时间戳"
      }
    }
  }
}'
```
