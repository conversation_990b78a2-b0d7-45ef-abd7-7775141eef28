# Elasticsearch数据定时清理任务配置
# 每天凌晨2点执行数据清理脚本
0 2 * * * /bin/bash /path/to/delete_es_data.sh

# 或者每周日凌晨2点执行（如果不需要每天执行）
# 0 2 * * 0 /bin/bash /path/to/delete_es_data.sh

# 或者每月1号凌晨2点执行
# 0 2 1 * * /bin/bash /path/to/delete_es_data.sh

# 使用方法：
# 1. 将delete_es_data.sh脚本放到合适的目录（如/opt/scripts/）
# 2. 给脚本添加执行权限：chmod +x /opt/scripts/delete_es_data.sh
# 3. 编辑crontab：crontab -e
# 4. 添加上述定时任务配置行
# 5. 保存并退出

# 注意事项：
# - 请根据实际脚本路径修改上述配置中的路径
# - 确保运行crontab的用户有足够权限访问ES和写入日志文件
# - 建议先手动执行脚本测试无误后再添加到crontab
