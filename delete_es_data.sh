#!/bin/bash

# Elasticsearch数据定时删除脚本
# 删除device_ai索引中3个月前的数据（基于timestamp字段）
# 删除meter_cur_data_es索引中3个月前的数据（基于dataTime字段）

# Elasticsearch服务器配置
ES_HOST="localhost"
ES_PORT=19500

# 日志文件路径
LOG_FILE="/var/log/es_data_cleanup.log"

# 记录日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 开始执行
log_message "开始执行ES数据清理任务"

# 计算3个月前的时间戳（秒）
CURRENT_DATE=$(date +%s)
THREE_MONTHS_AGO_SECONDS=$((CURRENT_DATE - (90 * 86400)))

# 计算3个月前的时间戳（毫秒，用于meter_cur_data_es索引）
THREE_MONTHS_AGO_MILLIS=$((THREE_MONTHS_AGO_SECONDS * 1000))

log_message "当前时间戳: $CURRENT_DATE"
log_message "3个月前时间戳(秒): $THREE_MONTHS_AGO_SECONDS"
log_message "3个月前时间戳(毫秒): $THREE_MONTHS_AGO_MILLIS"

# 删除device_ai索引中的旧数据
log_message "开始删除device_ai索引中3个月前的数据..."

DEVICE_AI_QUERY=$(cat <<EOF
{
  "query": {
    "range": {
      "timestamp": {
        "lt": "$THREE_MONTHS_AGO_SECONDS"
      }
    }
  }
}
EOF
)

log_message "device_ai删除查询条件: $DEVICE_AI_QUERY"

# 发送删除请求 - device_ai
DEVICE_AI_RESPONSE=$(curl -s -X POST "http://$ES_HOST:$ES_PORT/device_ai/_delete_by_query" \
    -H 'Content-Type: application/json' \
    -d "$DEVICE_AI_QUERY")

if [ $? -eq 0 ]; then
    log_message "device_ai索引删除请求发送成功"
    log_message "删除响应: $DEVICE_AI_RESPONSE"
    
    # 解析删除的文档数量
    DELETED_COUNT=$(echo "$DEVICE_AI_RESPONSE" | grep -o '"deleted":[0-9]*' | cut -d':' -f2)
    if [ -n "$DELETED_COUNT" ]; then
        log_message "device_ai索引成功删除 $DELETED_COUNT 条记录"
    fi
else
    log_message "device_ai索引删除请求发送失败"
fi

# 删除meter_cur_data_es索引中的旧数据
log_message "开始删除meter_cur_data_es索引中3个月前的数据..."

METER_DATA_QUERY=$(cat <<EOF
{
  "query": {
    "range": {
      "dataTime": {
        "lt": $THREE_MONTHS_AGO_MILLIS
      }
    }
  }
}
EOF
)

log_message "meter_cur_data_es删除查询条件: $METER_DATA_QUERY"

# 发送删除请求 - meter_cur_data_es
METER_DATA_RESPONSE=$(curl -s -X POST "http://$ES_HOST:$ES_PORT/meter_cur_data_es/_delete_by_query" \
    -H 'Content-Type: application/json' \
    -d "$METER_DATA_QUERY")

if [ $? -eq 0 ]; then
    log_message "meter_cur_data_es索引删除请求发送成功"
    log_message "删除响应: $METER_DATA_RESPONSE"
    
    # 解析删除的文档数量
    DELETED_COUNT=$(echo "$METER_DATA_RESPONSE" | grep -o '"deleted":[0-9]*' | cut -d':' -f2)
    if [ -n "$DELETED_COUNT" ]; then
        log_message "meter_cur_data_es索引成功删除 $DELETED_COUNT 条记录"
    fi
else
    log_message "meter_cur_data_es索引删除请求发送失败"
fi

# 检查索引状态（可选）
log_message "检查索引状态..."

# 检查device_ai索引状态
DEVICE_AI_COUNT=$(curl -s -X GET "http://$ES_HOST:$ES_PORT/device_ai/_count" | grep -o '"count":[0-9]*' | cut -d':' -f2)
if [ -n "$DEVICE_AI_COUNT" ]; then
    log_message "device_ai索引当前文档数量: $DEVICE_AI_COUNT"
fi

# 检查meter_cur_data_es索引状态
METER_DATA_COUNT=$(curl -s -X GET "http://$ES_HOST:$ES_PORT/meter_cur_data_es/_count" | grep -o '"count":[0-9]*' | cut -d':' -f2)
if [ -n "$METER_DATA_COUNT" ]; then
    log_message "meter_cur_data_es索引当前文档数量: $METER_DATA_COUNT"
fi

log_message "ES数据清理任务执行完成"

# 清理旧的日志文件（保留最近30天的日志）
find /var/log -name "es_data_cleanup.log*" -mtime +30 -delete 2>/dev/null

echo "数据清理任务完成，详细日志请查看: $LOG_FILE"
