#!/bin/bash

# Elasticsearch服务器地址和端口
ES_HOST="localhost"
ES_PORT=9200
INDEX_NAME="charging_order_detail"

# 当前日期（毫秒）
CURRENT_DATE=$(date +%s)

# 计算5个月前的日期（以毫秒为单位）
FIVE_MONTHS_AGO=$(((CURRENT_DATE - (113 * 86400)) * 1000))

# Elasticsearch查询
QUERY=$(cat <<EOF
{
  "query": {
    "range": {
      "updateTime": {
        "lt": $FIVE_MONTHS_AGO
      }
    }
  }
}
EOF
)

# 打印入参
echo "$QUERY"

# 发送删除请求
nohup curl -X POST "http://$ES_HOST:$ES_PORT/$INDEX_NAME/_delete_by_query" -H 'Content-Type: application/json' -d "$QUERY" >/dev/null 2>&1 &

# 打印查询命令
echo "curl -X GET 'http://localhost:9200/charging_order_detail/_search' -H 'Content-Type: application/json' -d '{\"size\":1,\"query\":{\"range\":{\"updateTime\":{\"lt\":\"$FIVE_MONTHS_AGO\"}}}}'"


# 检查curl命令是否成功执行
if [ $? -eq 0 ]; then
  echo "Data deletion request sent successfully."
else
  echo "Failed to send data deletion request."
fi